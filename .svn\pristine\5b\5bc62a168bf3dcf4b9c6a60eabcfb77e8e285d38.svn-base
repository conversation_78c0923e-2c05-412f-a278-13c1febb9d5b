package org.easitline.common.db.helper;

/**
 * SQL优化工具类
 * 提供各种SQL语句的优化功能，主要用于提升查询性能
 * 
 * 主要功能：
 * - 移除COUNT查询中的ORDER BY子句以提升性能
 * - 支持复杂SQL解析（引号、注释、括号层级）
 * - 只处理最外层SQL结构，不影响子查询
 * 
 * 使用示例：
 * // 优化COUNT查询
 * String optimizedSql = SqlOptimizer.removeOrderByForCount("SELECT COUNT(*) FROM table ORDER BY id");
 * // 结果: "SELECT COUNT(*) FROM table"
 * 
 * // 复杂SQL示例
 * String complexSql = "SELECT * FROM (SELECT * FROM table ORDER BY id) t ORDER BY name";
 * String result = SqlOptimizer.removeOrderByForCount(complexSql);
 * // 结果: "SELECT * FROM (SELECT * FROM table ORDER BY id) t" (只移除最外层ORDER BY)
 */
public class SqlOptimizer {

    /**
     * 移除SQL中的ORDER BY子句，用于COUNT查询性能优化
     * 只移除最外层的ORDER BY，不影响子查询中的ORDER BY
     * 支持各种空白字符组合、换行、注释等复杂情况
     *
     * @param sql 原始SQL语句
     * @return 移除ORDER BY后的SQL语句
     */
    public static String removeOrderByForCount(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        sql = sql.trim();

        // 查找最后一个ORDER BY的位置
        int lastOrderByIndex = -1;
        int parenthesesLevel = 0;
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean inLineComment = false;
        boolean inBlockComment = false;

        // 从前往后扫描，记录括号层级、引号状态和注释状态
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            char nextChar = (i + 1 < sql.length()) ? sql.charAt(i + 1) : '\0';

            // 处理行注释 --
            if (!inSingleQuote && !inDoubleQuote && !inBlockComment && c == '-' && nextChar == '-') {
                inLineComment = true;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理块注释开始 /*
            if (!inSingleQuote && !inDoubleQuote && !inLineComment && c == '/' && nextChar == '*') {
                inBlockComment = true;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理块注释结束 */
            if (inBlockComment && c == '*' && nextChar == '/') {
                inBlockComment = false;
                i++; // 跳过下一个字符
                continue;
            }

            // 处理行注释结束（换行）
            if (inLineComment && (c == '\n' || c == '\r')) {
                inLineComment = false;
            }

            // 如果在注释内，跳过
            if (inLineComment || inBlockComment) {
                continue;
            }

            // 处理引号状态
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }

            // 如果在引号内，跳过
            if (inSingleQuote || inDoubleQuote) {
                continue;
            }

            // 处理括号层级
            if (c == '(') {
                parenthesesLevel++;
            } else if (c == ')') {
                parenthesesLevel--;
            }

            // 只在最外层（parenthesesLevel == 0）查找ORDER BY
            if (parenthesesLevel == 0) {
                int orderByPos = findOrderByAt(sql, i);
                if (orderByPos != -1) {
                    // 确保ORDER BY前面是空白字符或者是SQL开始
                    if (orderByPos == 0 || Character.isWhitespace(sql.charAt(orderByPos - 1))) {
                        lastOrderByIndex = orderByPos;
                        // 继续查找，找到最后一个ORDER BY
                    }
                }
            }
        }

        // 如果找到了最外层的ORDER BY，则移除它
        if (lastOrderByIndex != -1) {
            return sql.substring(0, lastOrderByIndex).trim();
        }

        return sql;
    }

    /**
     * 在指定位置查找ORDER BY关键字，支持ORDER和BY之间的各种空白字符
     *
     * @param sql SQL字符串
     * @param startPos 开始查找的位置
     * @return 如果找到ORDER BY则返回ORDER的起始位置，否则返回-1
     */
    private static int findOrderByAt(String sql, int startPos) {
        if (startPos >= sql.length()) {
            return -1;
        }

        String remaining = sql.substring(startPos).toLowerCase();

        // 检查是否以"order"开头
        if (!remaining.startsWith("order")) {
            return -1;
        }

        // 检查ORDER后面是否跟着空白字符和BY
        int pos = 5; // "order"的长度

        // 跳过ORDER后面的空白字符
        while (pos < remaining.length() && Character.isWhitespace(remaining.charAt(pos))) {
            pos++;
        }

        // 检查是否是"by"
        if (pos + 2 <= remaining.length() && remaining.substring(pos, pos + 2).equals("by")) {
            // 确保BY后面是空白字符或字符串结束
            if (pos + 2 == remaining.length() || Character.isWhitespace(remaining.charAt(pos + 2))) {
                return startPos;
            }
        }

        return -1;
    }
}
