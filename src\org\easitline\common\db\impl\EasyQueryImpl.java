package org.easitline.common.db.impl;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.ConnectionMetaData;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyCallableStatement;
import org.easitline.common.db.EasyPreparedStatement;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.SqlHelper;
import org.easitline.common.db.helper.ConnectionHelper;
import org.easitline.common.db.helper.SqlBuilderHelper;
import org.easitline.common.db.helper.SqlOptimizer;
import org.easitline.common.db.helper.TimeoutControlHelper;
import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.db.log.JDBCLogger;


/**
 * EasyQuery 的实现类,提供数据库操作的核心功能
 * 主要功能包括:
 * 1. 事务管理(begin/commit/rollback)
 * 2. 数据库连接管理
 * 3. SQL执行(查询/更新/删除等)
 * 4. 结果集映射
 * 5. 分页查询支持
 */

public class EasyQueryImpl extends EasyQuery {
	
	/** 事务连接 */
	private java.sql.Connection transactionConnection;
	/** 数据库连接元数据 */
	private ConnectionMetaData metaData;
	/** 日志记录器 */
	private Logger logger;
	/** 字段名转换设置: 0-不转换 1-小写 2-大写 3-驼峰 */
	private int convertField = 2;
	/** 是否显示完整SQL */
	private boolean showFullSql = false;
	/** 是否为子查询 */
	private boolean subQuery = false;
	/** 自定义分页SQL */
	private String pageSql = null;
	/** 查询最大记录数,默认20000 */
	private int maxRow = 20000;
	/** SQL执行超时时间(秒),默认10秒 */
	private int timeout = 10;

	
	
	public EasyQueryImpl(String url, String user, String password) {
		metaData = new ConnectionMetaData(url, user, password);
	}

	public EasyQueryImpl(String appName, String appDatasourceName) {
		metaData = new ConnectionMetaData(appName, appDatasourceName);
	}

	public EasyQueryImpl(String sysDatasourceName) {
		metaData = new ConnectionMetaData(sysDatasourceName);
	}
	
	public EasyQueryImpl(Connection conn) {
		this.transactionConnection = conn;
		// 创建基于外部连接的ConnectionMetaData，用于数据库类型判断
		this.metaData = new ConnectionMetaData(conn);
	}

	/** 开始数据库事务 */
	public void begin() throws SQLException {
		this.transactionConnection = ConnectionHelper.beginTransaction(this.metaData, this.transactionConnection);
	}

	/** @deprecated 拼写错误,使用rollback */
	@Deprecated
	public void roolback() throws SQLException {
		rollback();
	}

	/** 回滚数据库事务 */
	public void rollback() throws SQLException {
		try {
			ConnectionHelper.rollbackTransaction(this.transactionConnection);
		} catch (SQLException ex) {
			throw ex;
		} finally {
			ConnectionHelper.closeConnection(this.transactionConnection, this.logger);
			this.transactionConnection = null;
		}
	}

	/** 提交数据库事务 */
	public void commit() throws SQLException {
		try {
			ConnectionHelper.commitTransaction(this.transactionConnection);
		} catch (SQLException ex) {
			throw ex;
		} finally {
			ConnectionHelper.closeConnection(this.transactionConnection, this.logger);
			this.transactionConnection = null;
		}
	}

	/** 获取数据库连接 */
	public java.sql.Connection getConnection() throws SQLException {
		return ConnectionHelper.getConnection(this.metaData, this.transactionConnection);
	}

	/** 创建预编译语句对象 */
	public EasyPreparedStatement preparedStatement(String sql) throws SQLException {
		return new EasyPreparedStatement(this.getConnection().prepareStatement(sql));
	}

	/** 创建存储过程调用对象 */
	public EasyCallableStatement callableStatement(String sql) throws SQLException {
		return new EasyCallableStatement(this.getConnection().prepareCall(sql));
	}

	/** 设置查询最大记录数 */
	public void setMaxRow(int maxCount) {
		if( maxCount == 0 || maxCount > maxRow ) {
			this.maxRow = 20000;
		}else {
			this.maxRow = maxCount;
		}

	}
	
	public void setLogger(Logger logger) {
		this.logger=logger;
	}
	
	public int getTimeout() {
		return timeout;
	}

	public void setTimeout(int timeout) {
		if(timeout>180) {
			this.timeout = 180;
			JDBCLogger.getLogger().warn("setTimeout("+timeout+") - "+SqlHelper.getStackTrace(Thread.currentThread().getStackTrace()));
		}else {
			this.timeout = timeout;
		}
	}


	
	public boolean isShowFullSql() {
		return showFullSql;
	}

	public void setShowFullSql(boolean showFullSql) {
		this.showFullSql = showFullSql;
	}
	
	public String getPageSql() {
		return pageSql;
	}

	public void setPageSql(String pageSql) {
		this.pageSql = pageSql;
	}
	
	
	public boolean isSubQuery() {
		return subQuery;
	}

	public void setSubQuery(boolean subQuery) {
		this.subQuery = subQuery;
	}



	/**
	 * 设置字段返回大小写
	 * @param toUpperCase
	 */
	public void setConvertField(int convertField) {
		this.convertField = convertField;
	}
	

	/**
	 * 执行 update 、delete、create的等批操作
	 * @param sqlList
	 * arrayList -> sql 保存每一条要执行的sql语句
	 * @throws SQLException
	 *  执行失败抛出异常
	 */
	public int[] executeBatch(List<String> sqlList) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.Statement stmt = null;
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createStatement(conn);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建Statement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建Statement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					if (i == 2) {
						throw new SQLException("无法创建Statement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建Statement，连接失败");
			}
			for (int i = 0; i < sqlList.size(); i++) {
				stmt.addBatch(sqlList.get(i));
			}
			return stmt.executeBatch();
		} catch (SQLException ex) {
			throw ex;
		} finally {
			this.close(null, stmt, conn);
		}
	}
	
	public void setStmtMaxRow(java.sql.Statement stmt) throws SQLException {
		if(stmt!=null) {
			stmt.setMaxRows(this.maxRow);
		}
	}

	/**
	 * 创建statement
	 */
	private java.sql.Statement createStatement(Connection conn) throws SQLException {
		java.sql.Statement stmt = conn.createStatement();
		this.setStmtMaxRow(stmt);
		stmt.setQueryTimeout(this.timeout+2);
		return stmt;
	}

	/**
	 * 创建statement
	 */
	private java.sql.PreparedStatement createPreparedStatement(Connection conn,String sql,Object[] params)throws SQLException {
		java.sql.PreparedStatement stmt = conn.prepareStatement(sql);
		this.setStmtMaxRow(stmt);
		//数据库超时往往要大于设计的超时时间 否则客户端会没有等到数据库执行sql超时就直接退出，又重新建立了新的链接了
		stmt.setQueryTimeout(this.timeout+2);
		return stmt;
	}
	

	/**
	 * 释放连接
	 */
	private void close(java.sql.ResultSet rs, java.sql.Statement stmt, java.sql.Connection conn) {
		ConnectionHelper.closeResources(rs, stmt, conn, this.transactionConnection, this.logger);
	}


	@Override
	public boolean queryForExist(String sql, Object... params) throws SQLException {
		if (this.queryForInt(sql, params) > 0) {
			return true;
		}
		return false;
	}

	@Override
	public int queryForInt(String sql, Object... params) throws SQLException {
		// 自动移除ORDER BY子句以优化COUNT查询性能
		String optimizedSql = SqlOptimizer.removeOrderByForCount(sql);
		EasyRow easyRow = this.queryForRow(optimizedSql, params);
		if(easyRow==null||StringUtils.isBlank(easyRow.getColumnValue(1))){
			return 0;
		}
		return Integer.parseInt(easyRow.getColumnValue(1));
	}

	@Override
	public String queryForString(String sql, Object... params) throws SQLException {
		EasyRow easyRow = this.queryForRow(sql, params);
		if(easyRow == null) return null;
		String retValue = easyRow.getColumnValue(1);
		if(retValue == null) return "";
		return retValue;
	}

	@Override
	public List<EasyRow> queryForList(String sql, Object... args) throws SQLException {
		return this.queryForList(sql, args, -1, -1);
	}
	


	@Override
	public EasyRow queryForRow(String sql, Object... params) throws SQLException {
		return  this.queryForRow(sql, params,new EasyRowMapperImpl());
	}

	@Override
	public void execute(String sql, Object... params) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (SQLException ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw ex;
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}

			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, params[i - 1]);
				}
			}

			// 使用客户端超时控制执行语句
			final PreparedStatement finalStmt = stmt;
			TimeoutControlHelper.executeWithTimeout(finalStmt, this.timeout, () -> {
				finalStmt.execute();
				return null;
			});
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}
	}
	
	public  Map<String, String> findById(EasyRecord record,String columns) throws SQLException {
		return findByIdLoadColumns(record,columns);
	}
	
	public  Map<String, String> findById(EasyRecord record) throws SQLException {
		return findByIdLoadColumns(record,"*");
	}
	
	
	
	@Override
	public boolean update(EasyRecord record) throws SQLException{
		//拼装SQL
		StringBuilder sql = new StringBuilder();
		List<Object> paras = new ArrayList<Object>();
		SqlBuilderHelper.trimPrimaryKeys(record.getPrimaryKeys());
		SqlBuilderHelper.forDbUpdate(record.getTableName(),record.getPrimaryKeys(),record,sql,paras);
		//执行更新语句
		int result = 0;
		Object[] params=paras.toArray();
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw new SQLException("无法创建PreparedStatement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}
			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, params[i - 1]);
				}
			}

			result=stmt.executeUpdate();
			return result >= 1;
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), params,metaData);
			this.close(null, stmt, conn);
		}
	}
	
	@Override
	public boolean save(EasyRecord record) throws SQLException{
		//拼装SQL
		StringBuilder sql = new StringBuilder();
		List<Object> paras = new ArrayList<Object>();
		SqlBuilderHelper.trimPrimaryKeys(record.getPrimaryKeys());
		SqlBuilderHelper.forDbSave(record.getTableName(),record.getPrimaryKeys(),record,sql,paras);
		//执行新增语句
		int result = 0;
		Object[] params=paras.toArray();
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw new SQLException("无法创建PreparedStatement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}

			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, params[i - 1]);
				}
			}

			result=stmt.executeUpdate();
			return result >= 1;
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), params,metaData);
			this.close(null, stmt, conn);
		}
		
	}
	
	private  Map<String, String> findByIdLoadColumns(EasyRecord record, String columns) throws SQLException {
		SqlBuilderHelper.trimPrimaryKeys(record.getPrimaryKeys());
		String sql = SqlBuilderHelper.forDbFindById(record.getTableName(),record.getPrimaryKeys(),columns);
		List<Map<String, String>> result=queryForList(sql, record.getPrimaryValues(),new MapRowMapperImpl());
		return result.size() > 0 ? result.get(0) : null;
	}
	
	@Override
	public  boolean deleteById(EasyRecord record) throws SQLException{
		boolean flag=true;//执行结果
		//生成Sql
		StringBuilder sql = SqlBuilderHelper.forDbDeleteById(record);
		//执行
		Object[] idValues=record.getPrimaryValues();
	 	java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql.toString(),idValues);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw new SQLException("无法创建PreparedStatement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}
			if (idValues != null) {
				for (int i = 1; i <= idValues.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, idValues[i - 1]);
				}
			}
			flag = stmt.executeUpdate() >= 1;
		} catch (SQLException ex) {
			flag=false;
			SqlHelper.errorLogOut(this.logger,ex, sql.toString(), idValues,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql.toString(), idValues,metaData);
			this.close(null, stmt, conn);
		}
		return flag;
	}

	@Override
	public int executeUpdate(String sql, Object... params) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		try {
			for(int i = 0;i<3;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 2) {
						throw new SQLException("无法创建PreparedStatement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}

			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, params[i - 1]);
				}
			}

			// 使用客户端超时控制执行更新
			final PreparedStatement finalStmt = stmt;
			return TimeoutControlHelper.executeWithTimeout(finalStmt, this.timeout, () -> finalStmt.executeUpdate());
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}

	}

	/**
	 * 执行查询
	 * @param sql
	 * @param params 参数对象
	 * @param pageNum 当前页
	 * @param pageSize 每页记录数
	 */
	@Override
	public List<EasyRow> queryForList(String sql, Object[] params, int pageNum, int pageSize) throws SQLException {
		return queryForList(sql,params,pageNum,pageSize,new EasyRowMapperImpl());
	}

	/**
	 * 把ResultSet对象转化成List<EasyRow>
	 */
	@SuppressWarnings("unchecked")
	private <T> List<T> toList(ResultSet rs, EasyRowMapper<T> rowMapper,int convertField) throws SQLException {
		List<T> rows = new ArrayList<T>();
		while (rs.next()) {
			 rows.add((T) rowMapper.mapRow(rs,convertField));
		}
		return rows;
	}

	/**
	 * 设定分页信息
	 * @param sql
	 * @param pageNum 当前页
	 * @param pageSize 每页显示记录数
	 * @return
	 */
	private String setPagination(String sql, int pageNum, int pageSize) {
		return PaginationSqlBuilder.setPagination(sql, pageNum, pageSize, this.getTypes(), this.getPageSql(), this.isSubQuery());
	}
	
	
	@Override
	public DBTypes getTypes() {  //通过连接来判断数据库类型
		return this.metaData.getDriverType();
	}
	

	@Override
	public <T> List<T> queryForList(String sql, Object[] params, EasyRowMapper<T> rowMapper) throws SQLException {
		return this.queryForList(sql, params, -1,-1,rowMapper,99);  //一般查询，不带分页
	}
	
	private <T> List<T> queryForList(String sql, Object[] params, int pageNum, int pageSize, EasyRowMapper<T> rowMapper,int type) throws SQLException {
		java.sql.Connection conn = null;
		java.sql.PreparedStatement stmt = null;
		long exetime = System.currentTimeMillis();
		sql = SqlBuilderHelper.addQueryTimeoutHint(sql, this.getTypes(), this.timeout);
		// 设定分页信息
		if(type == 2){
			sql = this.setPagination(sql, pageNum, pageSize);
		}
		List<T> list = null;
		try {
			for(int i = 0;i<2;i++){
				try {
					conn = this.getConnection();
					stmt = this.createPreparedStatement(conn, sql,params);
					break;
				} catch (Exception ex) {
					if (this.logger != null) {
						this.logger.error("创建PreparedStatement失败,原因: "+ex.getMessage());
					}
					JDBCErrorLogger.getLogger().error("创建PreparedStatement失败,原因: "+ex.getMessage());
					this.close(null, stmt, conn);
					// 如果是最后一次重试，抛出异常
					if (i == 1) {
						throw new SQLException("无法创建PreparedStatement，连接失败", ex);
					}
				}
			}
			if (stmt == null) {
				throw new SQLException("无法创建PreparedStatement，连接失败");
			}
			if(type == 1)  stmt.setMaxRows(1);  //设置查询返回记录数为1

			if (params != null) {
				for (int i = 1; i <= params.length; i++) {
					SqlBuilderHelper.setParam(stmt, i, params[i - 1]);
				}
			}

			// 使用客户端超时控制执行查询
			final PreparedStatement finalStmt = stmt;
			ResultSet rs = TimeoutControlHelper.executeWithTimeout(finalStmt, this.timeout, () -> finalStmt.executeQuery());
			if(rs!=null){
				list = this.toList(rs, rowMapper,this.convertField);
			}

			if(list != null && list.size()>10000){
				SqlHelper.listSizeLogOut(this.logger,list.size(), sql, params);
			}
			
		} catch (SQLException ex) {
			SqlHelper.errorLogOut(this.logger,ex, sql, params,metaData);
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			SqlHelper.timeoutLogOut(this.logger,exetime, sql, params,metaData);
			this.close(null, stmt, conn);
		}
		return list;
	}
	
	
	@Override
	public <T> List<T> queryForList(String sql, Object[] params, int pageNum, int pageSize, EasyRowMapper<T> rowMapper) throws SQLException {
		 return this.queryForList(sql, params, pageNum, pageSize, rowMapper,2);
	}

	@Override
	public <T> T queryForRow(String sql, Object[] params, EasyRowMapper<T> rowMapper) throws SQLException {
		List<T> list =  this.queryForList(sql, params, -1, -1, rowMapper,1);
		if(list == null) return null;
		if(list.size()>0) return list.get(0);
		return null;
	}


	/**
	 * 执行批量查询 
	 */
	public int[] executeBatch(String sql, List<Object[]> paramsList) throws SQLException {
		java.sql.Connection conn = null;
		PreparedStatement ps = null ;
		long exetime = System.currentTimeMillis();
		try {
			conn = this.getConnection();
			conn.setAutoCommit(false);
			ps   = conn.prepareStatement(sql);
			for (int i = 0; i < paramsList.size(); i++) {
				Object[] params = paramsList.get(i);
				for(int j = 0;j < params.length;j++){
					ps.setObject(j+1, params[j]);
				}
				ps.addBatch(); 
			}
			int[] result =  ps.executeBatch();
			conn.commit();
			return result;
		} catch (SQLException ex) {
			if(conn!=null) conn.rollback();
			throw ex;
		} finally {
			exetime = System.currentTimeMillis()-exetime;
			if(paramsList!=null) {
				SqlHelper.timeoutLogOut(this.logger,exetime, sql, new Object[] {paramsList},metaData);
			}else {
				SqlHelper.timeoutLogOut(this.logger,exetime, sql, null,metaData);
			}
			this.close(null, ps, conn);
		}
	}
	
}
