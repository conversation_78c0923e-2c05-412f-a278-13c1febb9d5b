package org.easitline.common.db.impl;

import org.easitline.common.db.DBTypes;

/**
 * 数据库分页SQL生成工具类
 */
public class PaginationSqlBuilder {
    
    /**
     * 根据数据库类型生成对应的分页SQL
     *
     * @param sql 原始SQL语句
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param dbType 数据库类型
     * @param pageSql 自定义分页SQL,如果不为空则直接返回
     * @param isSubQuery 是否子查询
     * @return 分页SQL
     */
    public static String setPagination(String sql, int pageNum, int pageSize, DBTypes dbType, String pageSql, boolean isSubQuery) {
        // 不分页的情况
        if (pageNum == -1 && pageSize == -1) {
            return sql;
        }
        
        // 参数校验
        if (pageNum <= 0){
            pageNum = 1;
        }
        if (pageSize <= 0){
            pageSize = 50;
        }
        
        // 自定义分页SQL
        if(pageSql != null){
            return pageSql;
        }

        // 根据数据库类型生成分页SQL
        if (dbType == DBTypes.MYSQL || dbType == DBTypes.SQLITE) {
            return toMysqlPageSql(sql, pageNum, pageSize, isSubQuery);
        } else if (dbType == DBTypes.PostgreSql || dbType == DBTypes.OPENGAUSS || dbType == DBTypes.OSCAR) {
            return toPostgresqlPageSql(sql, pageNum, pageSize, isSubQuery);
        } else if (dbType == DBTypes.DB2) {
            return toDb2PageSql(sql, pageNum, pageSize);
        } else if (dbType == DBTypes.ORACLE || dbType == DBTypes.DERBY || dbType == DBTypes.DAMENG) {
            return toOraclePageSql(sql, pageNum, pageSize);
        } else if(dbType == DBTypes.SYBASE){
            return toSybasePageSql(sql, pageNum, pageSize);
        } else if(dbType == DBTypes.SQLSERVER) {
            return toSqlserverPageSql(sql, pageNum, pageSize);
        } else {
            return toMysqlPageSql(sql, pageNum, pageSize, isSubQuery);
        }
    }

    /**
     * MySQL/SQLite分页SQL生成
     * 使用 LIMIT 语法
     */
    private static String toMysqlPageSql(String sql, int pageNo, int pageSize, boolean isSubQuery) {
        if(isSubQuery) {
            return "SELECT * FROM (" + sql + ") A_A LIMIT " + (pageNo - 1) * pageSize + "," + pageSize;
        } else {
            return sql + " LIMIT " + (pageNo - 1) * pageSize + "," + pageSize;
        }
    }

    /**
     * PostgreSQL分页SQL生成
     * 使用 LIMIT OFFSET 语法
     */
    private static String toPostgresqlPageSql(String sql, int pageNo, int pageSize, boolean isSubQuery) {
        if(isSubQuery) {
            return "SELECT * FROM (" + sql + ") A_A LIMIT " + pageSize + " offset " + (pageNo - 1) * pageSize;
        } else {
            return sql + " LIMIT " + pageSize + " offset " + (pageNo - 1) * pageSize;
        }
    }

    /**
     * Sybase分页SQL生成
     * TODO: 待实现
     */
    private static String toSybasePageSql(String sql, int pageNo, int pageSize) {
        return sql;
    }

    /**
     * DB2分页SQL生成
     * TODO: 待实现具体分页逻辑
     */
    private static String toDb2PageSql(String sql, int pageNo, int pageSize) {
        return sql;
    }

    /**
     * Oracle分页SQL生成
     * 使用 ROWNUM 语法
     */
    private static String toOraclePageSql(String sql, int pageNo, int pageSize) {
        int start = (pageNo - 1) * pageSize;
        int end = start + pageSize;
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM (SELECT ROWNUM R_R,A_A.* FROM (")
          .append(sql)
          .append(") A_A WHERE ROWNUM<=")
          .append(end)
          .append(") B_B WHERE R_R>")
          .append(start);
        return sb.toString();
    }

    /**
     * SQL Server分页SQL生成
     * 使用 ROW_NUMBER() OVER 语法
     * 
     * 分页SQL结构:
     * SELECT * FROM 
     *   (SELECT ROW_NUMBER()OVER(ORDER BY _COL)_ROW_NUM,* FROM 
     *     (SELECT TOP start+pageSize _COL=0,* FROM SQL) _TA 
     *   ) _TB 
     * WHERE _ROW_NUM>start
     */
    private static String toSqlserverPageSql(String sql, int pageNo, int pageSize) {
        int start = (pageNo - 1) * pageSize;
        sql = sql.trim();
        if(sql.toLowerCase().startsWith("select")){
            sql = "select TOP 100 PERCENT " + sql.substring(6,sql.length());
        }

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT * FROM (SELECT ROW_NUMBER()OVER(ORDER BY _COL)_ROW_NUM,* FROM (SELECT TOP ")
          .append(start + pageSize)
          .append(" _COL=0,* FROM (")
          .append(sql)
          .append(") _TC) _TA) _TB WHERE _ROW_NUM>")
          .append(start);
        return sb.toString();
    }


}
